'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  CreditCard, 
  Calendar, 
  Settings, 
  UserPlus, 
  CalendarDays,
  Mail,
  MessageCircle,
  Bell
} from 'lucide-react';

interface NotificationTypesConfig {
  email: boolean;
  whatsapp: boolean;
  in_app: boolean;
}

interface NotificationTypesSectionProps {
  notificationTypes: Record<string, NotificationTypesConfig>;
  onNotificationTypesChange: (types: Record<string, NotificationTypesConfig>) => void;
  emailEnabled: boolean;
  whatsappEnabled: boolean;
}

const notificationTypeConfigs = [
  {
    key: 'payment',
    title: 'Pagamentos',
    description: 'Confirmações de pagamento, vencimentos e cobranças',
    icon: CreditCard,
    color: 'text-green-600 dark:text-green-400',
  },
  {
    key: 'class',
    title: 'Aulas',
    description: 'Lembre<PERSON> de aulas, cancelamentos e alterações',
    icon: Calendar,
    color: 'text-blue-600 dark:text-blue-400',
  },
  {
    key: 'enrollment',
    title: 'Matrículas',
    description: 'Confirmações de matrícula e renovações',
    icon: UserPlus,
    color: 'text-purple-600 dark:text-purple-400',
  },
  // {
  //   key: 'event',
  //   title: 'Eventos',
  //   description: 'Competições, seminários e eventos especiais',
  //   icon: CalendarDays,
  //   color: 'text-orange-600 dark:text-orange-400',
  // },
  {
    key: 'system',
    title: 'Sistema',
    description: 'Atualizações do sistema e manutenções',
    icon: Settings,
    color: 'text-gray-600 dark:text-gray-400',
  },
];

export function NotificationTypesSection({
  notificationTypes,
  onNotificationTypesChange,
  emailEnabled,
  whatsappEnabled,
}: NotificationTypesSectionProps) {
  const updateNotificationType = (
    typeKey: string,
    channel: keyof NotificationTypesConfig,
    enabled: boolean
  ) => {
    // Notificações in-app sempre ficam ativas
    if (channel === 'in_app') {
      return;
    }

    const updatedTypes = {
      ...notificationTypes,
      [typeKey]: {
        ...notificationTypes[typeKey],
        [channel]: enabled,
      },
    };
    onNotificationTypesChange(updatedTypes);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Tipos de Notificação</h3>
        <p className="text-sm text-muted-foreground">
          Configure quais tipos de notificação devem ser enviados por cada canal.
        </p>
      </div>

      <div className="grid gap-4">
        {notificationTypeConfigs.map((config) => {
          const IconComponent = config.icon;
          const typeConfig = notificationTypes[config.key] || {
            email: false,
            whatsapp: false,
            in_app: true,
          };

          return (
            <Card key={config.key}>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-3 text-base">
                  <div className={`p-2 rounded-lg bg-muted ${config.color}`}>
                    <IconComponent className="h-4 w-4" />
                  </div>
                  <div>
                    <div className="font-medium">{config.title}</div>
                    <div className="text-sm text-muted-foreground font-normal">
                      {config.description}
                    </div>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* E-mail */}
                  <div className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <Label className="text-sm">E-mail</Label>
                    </div>
                    <Switch
                      checked={typeConfig.email && emailEnabled}
                      disabled={!emailEnabled}
                      onCheckedChange={(checked) =>
                        updateNotificationType(config.key, 'email', checked)
                      }
                    />
                  </div>

                  {/* WhatsApp */}
                  <div className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-2">
                      <MessageCircle className="h-4 w-4 text-muted-foreground" />
                      <Label className="text-sm">WhatsApp</Label>
                    </div>
                    <Switch
                      checked={typeConfig.whatsapp && whatsappEnabled}
                      disabled={!whatsappEnabled}
                      onCheckedChange={(checked) =>
                        updateNotificationType(config.key, 'whatsapp', checked)
                      }
                    />
                  </div>

                  {/* In-App */}
                  <div className="flex items-center justify-between p-3 rounded-lg border bg-muted/30">
                    <div className="flex items-center gap-2">
                      <Bell className="h-4 w-4 text-muted-foreground" />
                      <Label className="text-sm">No App</Label>
                    </div>
                    <Switch
                      checked={true}
                      disabled={true}
                      onCheckedChange={() => {}}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="bg-muted/50 p-4 rounded-lg">
        <div className="flex items-start gap-3">
          <div className="bg-amber-100 dark:bg-amber-900/30 p-2 rounded-full">
            <Bell className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium">Dica</p>
            <p className="text-xs text-muted-foreground">
              Notificações no app estão sempre ativas para garantir que informações importantes 
              sejam sempre visíveis. E-mail e WhatsApp podem ser desativados individualmente 
              para cada tipo de notificação.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
